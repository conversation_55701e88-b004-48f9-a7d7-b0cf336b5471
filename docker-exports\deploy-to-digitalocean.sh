#!/bin/bash
# Enhanced deployment script for MVS-VR to DigitalOcean using exported images
# Supports verification and selective loading of missing/updated images

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MANIFEST_FILE="$SCRIPT_DIR/export-manifest.json"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    if [[ ! -f "$MANIFEST_FILE" ]]; then
        log_error "export-manifest.json not found in $SCRIPT_DIR"
        exit 1
    fi

    # Check if Docker is available
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker is not installed or not in PATH"
        exit 1
    fi

    # Check if docker-compose is available
    if ! command -v docker-compose >/dev/null 2>&1; then
        log_error "Docker Compose is not installed or not in PATH"
        exit 1
    fi

    log_success "Prerequisites check passed"
}

# Get expected files from manifest
get_expected_files() {
    if command -v jq >/dev/null 2>&1; then
        jq -r '.images[].file' "$MANIFEST_FILE" 2>/dev/null || {
            log_error "Failed to parse manifest with jq"
            return 1
        }
    else
        # Fallback if jq is not available
        grep '"file"' "$MANIFEST_FILE" | cut -d'"' -f4 || {
            log_error "Failed to parse manifest with fallback method"
            return 1
        }
    fi
}

# Get expected size from manifest
get_expected_size() {
    local filename="$1"
    if command -v jq >/dev/null 2>&1; then
        jq -r ".images[] | select(.file == \"$filename\") | .size" "$MANIFEST_FILE" 2>/dev/null || echo "0"
    else
        # Fallback parsing
        grep -A 10 "\"file\": \"$filename\"" "$MANIFEST_FILE" | grep '"size"' | head -1 | grep -o '[0-9]*' || echo "0"
    fi
}

# Get version from manifest
get_version() {
    if command -v jq >/dev/null 2>&1; then
        jq -r '.version' "$MANIFEST_FILE" 2>/dev/null || echo "unknown"
    else
        grep '"version"' "$MANIFEST_FILE" | cut -d'"' -f4 || echo "unknown"
    fi
}

# Check which files are missing or corrupted
check_files() {
    log_info "Checking exported image files..."

    local expected_files
    expected_files=$(get_expected_files)

    local missing_files=()
    local corrupted_files=()
    local valid_files=()

    while IFS= read -r filename; do
        if [[ -z "$filename" ]]; then
            continue
        fi

        local filepath="$SCRIPT_DIR/$filename"
        local expected_size
        expected_size=$(get_expected_size "$filename")

        if [[ ! -f "$filepath" ]]; then
            log_warning "Missing file: $filename"
            missing_files+=("$filename")
        elif [[ -n "$expected_size" ]] && [[ "$expected_size" != "0" ]]; then
            local actual_size
            actual_size=$(stat -c%s "$filepath" 2>/dev/null || wc -c < "$filepath" 2>/dev/null || echo "0")

            if [[ "$actual_size" != "$expected_size" ]]; then
                log_warning "Size mismatch for $filename (expected: $expected_size, actual: $actual_size)"
                corrupted_files+=("$filename")
            else
                log_success "Valid file: $filename"
                valid_files+=("$filename")
            fi
        else
            log_info "Cannot verify size for: $filename (no size in manifest)"
            valid_files+=("$filename")
        fi
    done <<< "$expected_files"

    log_info "File check summary:"
    log_info "  Valid files: ${#valid_files[@]}"
    log_info "  Missing files: ${#missing_files[@]}"
    log_info "  Corrupted files: ${#corrupted_files[@]}"

    if [[ ${#missing_files[@]} -gt 0 ]]; then
        log_error "Missing files detected. Please ensure all exported images are present:"
        printf '  - %s\n' "${missing_files[@]}"
        return 1
    fi

    if [[ ${#corrupted_files[@]} -gt 0 ]]; then
        log_error "Corrupted files detected. Please re-export these images:"
        printf '  - %s\n' "${corrupted_files[@]}"
        return 1
    fi

    return 0
}

# Load Docker images selectively
load_images() {
    log_info "Loading Docker images..."

    local version
    version=$(get_version)
    log_info "Loading images for version: $version"

    # Get list of currently loaded images
    local loaded_images
    loaded_images=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep -E ":(${version}|latest)" || echo "")

    local expected_files
    expected_files=$(get_expected_files)

    local loaded_count=0
    local skipped_count=0

    while IFS= read -r filename; do
        if [[ -z "$filename" ]]; then
            continue
        fi

        local filepath="$SCRIPT_DIR/$filename"

        if [[ ! -f "$filepath" ]]; then
            log_warning "Skipping missing file: $filename"
            continue
        fi

        # Extract service name from filename
        local service_name
        service_name=$(echo "$filename" | sed 's/-[0-9].*\.tar.*$//' | sed 's/-test-v[0-9].*\.tar.*$//')

        # Check if image is already loaded
        local image_loaded=false
        if echo "$loaded_images" | grep -q "${service_name}:${version}"; then
            log_info "Image already loaded: ${service_name}:${version}"
            image_loaded=true
            ((skipped_count++))
        fi

        if [[ "$image_loaded" == "false" ]]; then
            log_info "Loading: $filename"

            # Handle compressed files
            if [[ "$filename" == *.zip ]]; then
                if command -v unzip >/dev/null 2>&1; then
                    unzip -p "$filepath" | docker load
                else
                    log_error "unzip command not found, cannot load compressed file: $filename"
                    continue
                fi
            elif [[ "$filename" == *.gz ]]; then
                gunzip -c "$filepath" | docker load
            else
                docker load -i "$filepath"
            fi

            if [[ $? -eq 0 ]]; then
                log_success "Loaded: $filename"
                ((loaded_count++))
            else
                log_error "Failed to load: $filename"
                return 1
            fi
        fi
    done <<< "$expected_files"

    log_info "Image loading summary:"
    log_info "  Newly loaded: $loaded_count"
    log_info "  Already loaded: $skipped_count"

    # Show loaded images
    log_info "Currently loaded images:"
    docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep -E "(REPOSITORY|${version}|latest)" || echo "No matching images found"
}

# Deploy services
deploy_services() {
    log_info "Starting services with exported images..."

    # Check if docker-compose files exist
    local compose_files=()

    if [[ -f "docker-compose.yml" ]]; then
        compose_files+=("-f" "docker-compose.yml")
    fi

    if [[ -f "docker-compose.exported.yml" ]]; then
        compose_files+=("-f" "docker-compose.exported.yml")
    elif [[ -f "docker-compose.staging.yml" ]]; then
        compose_files+=("-f" "docker-compose.staging.yml")
    fi

    if [[ ${#compose_files[@]} -eq 0 ]]; then
        log_error "No docker-compose files found"
        return 1
    fi

    log_info "Using compose files: ${compose_files[*]}"

    # Stop existing services
    docker-compose "${compose_files[@]}" down || true

    # Start services
    docker-compose "${compose_files[@]}" up -d

    # Wait for services to start
    sleep 10

    log_success "Services started successfully"
}

# Check deployment health
check_health() {
    log_info "Checking deployment health..."

    # Show service status
    docker-compose ps

    # Check for any failed containers
    local failed_containers
    failed_containers=$(docker-compose ps --services --filter status=exited || echo "")

    if [[ -n "$failed_containers" ]]; then
        log_warning "Some containers have exited:"
        echo "$failed_containers"

        # Show logs for failed containers
        echo "$failed_containers" | while read -r service; do
            if [[ -n "$service" ]]; then
                log_info "Logs for $service:"
                docker-compose logs --tail=20 "$service"
            fi
        done
    else
        log_success "All services are running"
    fi
}

# Main deployment function
main() {
    log_info "Starting enhanced MVS-VR deployment to DigitalOcean..."

    # Check prerequisites
    check_prerequisites

    # Check files
    if ! check_files; then
        log_error "File check failed. Please fix the issues and try again."
        exit 1
    fi

    # Load images
    if ! load_images; then
        log_error "Image loading failed"
        exit 1
    fi

    # Deploy services
    if ! deploy_services; then
        log_error "Service deployment failed"
        exit 1
    fi

    # Check health
    check_health

    local version
    version=$(get_version)

    log_success "Deployment completed successfully!"
    log_info "Version: $version"
    log_info "Check service status with: docker-compose ps"
    log_info "View logs with: docker-compose logs -f"
}

# Run main function
main "$@"
