#!/bin/bash
# Deploy MVS-VR to DigitalOcean using exported images

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MANIFEST_FILE="$SCRIPT_DIR/export-manifest.json"

if [[ ! -f "$MANIFEST_FILE" ]]; then
    echo "Error: export-manifest.json not found"
    exit 1
fi

echo "Deploying MVS-VR to DigitalOcean..."

# Load images first
echo "Loading Docker images..."
if [[ -f "$SCRIPT_DIR/load-images.ps1" ]]; then
    powershell -File "$SCRIPT_DIR/load-images.ps1"
else
    "$SCRIPT_DIR/load-images.sh"
fi

# Extract version
VERSION=$(grep '"version"' "$MANIFEST_FILE" | cut -d'"' -f4)

echo "Starting services with exported images (version: $VERSION)..."
docker-compose -f docker-compose.yml -f docker-compose.exported.yml up -d

echo "Deployment completed!"
echo "Check service status with: docker-compose ps"
