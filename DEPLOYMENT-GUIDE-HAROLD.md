# MVS-VR v2 Deployment Guide for Harold

## 🔧 Your Configuration

- **SSH Key**: `C:\Users\<USER>\mvs-vr`
- **SSH Password**: `vectorax`
- **Username**: `vectorax`
- **Server**: `**************`
- **Email**: `<EMAIL>`

## 🚀 Quick Start Commands

### 1. Test Deployment (Recommended First)
```powershell
# Test with your personalized script
.\scripts\deploy-harold.ps1 -DryRun -Verbose

# Or use the main script directly
.\scripts\smart-deploy-staging.ps1 -SSHKey "C:\Users\<USER>\mvs-vr" -Username "vectorax" -DryRun -Verbose
```

### 2. Build and Export Docker Images
```powershell
# Build all services and create export files
.\scripts\export-docker-images.ps1 -Verbose
```

### 3. Deploy to Production
```powershell
# Deploy with your personalized script
.\scripts\deploy-harold.ps1 -Verbose

# Or use the main script directly
.\scripts\smart-deploy-staging.ps1 -SSHKey "C:\Users\<USER>\mvs-vr" -Username "vectorax" -Verbose
```

### 4. Force Deploy All Files
```powershell
# Force transfer all files (ignores change detection)
.\scripts\deploy-harold.ps1 -Force -Verbose
```

### 5. Deploy with Custom Manifest
```powershell
# Use a specific manifest file
.\scripts\deploy-harold.ps1 -ManifestPath "docker-exports\custom-manifest.json" -Verbose
```

## 📋 Available Scripts

### Your Personalized Script
- **File**: `scripts\deploy-harold.ps1`
- **Purpose**: Pre-configured with your credentials
- **Usage**: `.\scripts\deploy-harold.ps1 [OPTIONS]`

### Main Deployment Script
- **File**: `scripts\smart-deploy-staging.ps1`
- **Purpose**: Full-featured deployment with all options
- **Usage**: `.\scripts\smart-deploy-staging.ps1 [OPTIONS]`

### Export Scripts
- **File**: `scripts\export-docker-images.ps1`
- **Purpose**: Build and export all Docker images
- **Usage**: `.\scripts\export-docker-images.ps1 [OPTIONS]`

### Testing Script
- **File**: `scripts\test-deployment.ps1`
- **Purpose**: Validate deployment system
- **Usage**: `.\scripts\test-deployment.ps1 [OPTIONS]`

## 🔍 Common Options

| Option | Description |
|--------|-------------|
| `-DryRun` | Show what would be done without executing |
| `-Verbose` | Enable detailed output |
| `-Force` | Force transfer all files regardless of status |
| `-SkipVerification` | Skip file verification after transfer |
| `-Help` | Show help message |
| `-ManifestPath <PATH>` | Use custom manifest file |

## 🛡️ Security Notes

1. **SSH Key Password**: Your SSH key password (`vectorax`) is stored in the personalized script for convenience
2. **Production Security**: Consider using SSH agent or removing the password for production use
3. **Key Location**: Ensure your SSH key at `C:\Users\<USER>\mvs-vr` has proper permissions

## 📊 What the Smart Deployment Does

1. ✅ **Validates Prerequisites** - Checks SSH key, commands, and files
2. ✅ **Tests Connection** - Verifies server connectivity
3. ✅ **Analyzes Changes** - Only transfers files that changed
4. ✅ **Transfers Files** - Efficiently copies Docker images
5. ✅ **Verifies Transfer** - Confirms file integrity
6. ✅ **Deploys Services** - Loads images and starts containers
7. ✅ **Health Checks** - Verifies services are running

## 🔧 Troubleshooting

### SSH Connection Issues
```powershell
# Test SSH connection manually
ssh -i "C:\Users\<USER>\mvs-vr" vectorax@************** "echo 'Connection test'"
```

### Check Available Images
```powershell
# List built Docker images
docker images | findstr mvs-vr-v2
```

### Validate Deployment System
```powershell
# Run comprehensive tests
.\scripts\test-deployment.ps1 -Verbose
```

### View Logs
```powershell
# Check deployment logs on server
ssh -i "C:\Users\<USER>\mvs-vr" vectorax@************** "tail -f /opt/mvs-vr-v2/logs/*.log"
```

## 📁 File Structure

```
mvs-vr-v2/
├── scripts/
│   ├── deploy-harold.ps1          # Your personalized script
│   ├── smart-deploy-staging.ps1   # Main deployment script
│   ├── export-docker-images.ps1   # Image export script
│   └── test-deployment.ps1        # Testing script
├── docker-exports/
│   ├── export-manifest.json       # Deployment manifest
│   └── *.tar.zip                  # Exported Docker images
└── DEPLOYMENT-GUIDE-HAROLD.md     # This guide
```

## 🎯 Quick Reference

**Most Common Command:**
```powershell
.\scripts\deploy-harold.ps1 -DryRun -Verbose
```

**For Production Deployment:**
```powershell
.\scripts\deploy-harold.ps1 -Verbose
```

**For Help:**
```powershell
.\scripts\deploy-harold.ps1 -Help
```

---

**Happy Deploying! 🚀**
