# Test script for smart deployment functionality (PowerShell)
# This script validates the deployment scripts without actually deploying

param(
    [switch]$Verbose
)

# Script configuration
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectDir = Split-Path -Parent $ScriptDir

# Test results
$TestsPassed = 0
$TestsFailed = 0

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Cyan = "Cyan"
}

# Logging functions
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Colors.Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Colors.Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Colors.Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Colors.Red
}

function Write-Debug {
    param([string]$Message)
    if ($Verbose) {
        Write-Host "[DEBUG] $Message" -ForegroundColor $Colors.Cyan
    }
}

# Test function wrapper
function Invoke-Test {
    param(
        [string]$TestName,
        [scriptblock]$TestFunction
    )
    
    Write-Host ""
    Write-Info "Running test: $TestName"
    
    try {
        $result = & $TestFunction
        if ($result) {
            Write-Success "PASSED: $TestName"
            $script:TestsPassed++
        } else {
            Write-Error "FAILED: $TestName"
            $script:TestsFailed++
        }
    }
    catch {
        Write-Error "FAILED: $TestName - $($_.Exception.Message)"
        $script:TestsFailed++
    }
}

# Test 1: Check if required files exist
function Test-RequiredFiles {
    $requiredFiles = @(
        "$ProjectDir\deploy.sh",
        "$ScriptDir\smart-deploy-staging.sh",
        "$ScriptDir\smart-deploy-staging.ps1",
        "$ProjectDir\docker-exports\deploy-to-digitalocean.sh",
        "$ProjectDir\docker-exports\export-manifest.json"
    )
    
    foreach ($file in $requiredFiles) {
        if (-not (Test-Path $file)) {
            Write-Error "Required file missing: $file"
            return $false
        }
    }
    
    return $true
}

# Test 2: Validate manifest file structure
function Test-ManifestStructure {
    $manifestFile = "$ProjectDir\docker-exports\export-manifest.json"
    
    try {
        $manifest = Get-Content $manifestFile | ConvertFrom-Json
        
        # Check required fields
        if (-not $manifest.version) {
            Write-Error "Manifest missing 'version' field"
            return $false
        }
        
        if (-not $manifest.images) {
            Write-Error "Manifest missing 'images' field"
            return $false
        }
        
        if (-not $manifest.export_date) {
            Write-Error "Manifest missing 'export_date' field"
            return $false
        }
        
        # Check if images have required properties
        $imageCount = ($manifest.images | Get-Member -MemberType NoteProperty).Count
        
        if ($imageCount -eq 0) {
            Write-Error "No images found in manifest"
            return $false
        }
        
        Write-Info "Found $imageCount images in manifest"
        return $true
    }
    catch {
        Write-Error "Manifest file is not valid JSON: $($_.Exception.Message)"
        return $false
    }
}

# Test 3: Check if expected Docker export files exist
function Test-DockerExportFiles {
    $manifestFile = "$ProjectDir\docker-exports\export-manifest.json"
    $dockerExportsDir = "$ProjectDir\docker-exports"
    
    $missingFiles = @()
    $corruptedFiles = @()
    
    try {
        $manifest = Get-Content $manifestFile | ConvertFrom-Json
        
        foreach ($imageProperty in ($manifest.images | Get-Member -MemberType NoteProperty)) {
            $imageName = $imageProperty.Name
            $imageInfo = $manifest.images.$imageName
            $filename = $imageInfo.file
            
            if (-not $filename) {
                continue
            }
            
            $filepath = Join-Path $dockerExportsDir $filename
            if (-not (Test-Path $filepath)) {
                $missingFiles += $filename
            } else {
                # Check file size if available in manifest
                $expectedSize = $imageInfo.size
                
                if ($expectedSize -and $expectedSize -gt 0) {
                    $actualSize = (Get-Item $filepath).Length
                    
                    if ($actualSize -ne $expectedSize) {
                        $corruptedFiles += "$filename (expected: $expectedSize, actual: $actualSize)"
                    }
                }
            }
        }
    }
    catch {
        Write-Error "Failed to parse manifest: $($_.Exception.Message)"
        return $false
    }
    
    if ($missingFiles.Count -gt 0) {
        Write-Error "Missing Docker export files:"
        $missingFiles | ForEach-Object { Write-Error "  - $_" }
        return $false
    }
    
    if ($corruptedFiles.Count -gt 0) {
        Write-Error "Corrupted Docker export files (size mismatch):"
        $corruptedFiles | ForEach-Object { Write-Error "  - $_" }
        return $false
    }
    
    return $true
}

# Test 4: Validate PowerShell script syntax
function Test-ScriptSyntax {
    $psScript = "$ScriptDir\smart-deploy-staging.ps1"
    
    try {
        # Test PowerShell script syntax
        $errors = $null
        $null = [System.Management.Automation.PSParser]::Tokenize((Get-Content $psScript -Raw), [ref]$errors)
        
        if ($errors.Count -gt 0) {
            Write-Error "PowerShell script syntax errors:"
            $errors | ForEach-Object { Write-Error "  - Line $($_.Token.StartLine): $($_.Message)" }
            return $false
        }
        
        return $true
    }
    catch {
        Write-Error "PowerShell script syntax validation failed: $($_.Exception.Message)"
        return $false
    }
}

# Test 5: Test help functionality
function Test-HelpFunctionality {
    try {
        # Test PowerShell script help
        $helpOutput = & "$ScriptDir\smart-deploy-staging.ps1" -Help
        
        if ($LASTEXITCODE -eq 0) {
            Write-Info "PowerShell script help works"
            return $true
        } else {
            Write-Error "PowerShell script help failed"
            return $false
        }
    }
    catch {
        Write-Error "Help functionality test failed: $($_.Exception.Message)"
        return $false
    }
}

# Test 6: Check system dependencies
function Test-SystemDependencies {
    $requiredCommands = @("ssh", "scp")
    $optionalCommands = @("docker", "docker-compose")
    
    $missingRequired = @()
    $missingOptional = @()
    
    foreach ($cmd in $requiredCommands) {
        if (-not (Get-Command $cmd -ErrorAction SilentlyContinue)) {
            $missingRequired += $cmd
        }
    }
    
    foreach ($cmd in $optionalCommands) {
        if (-not (Get-Command $cmd -ErrorAction SilentlyContinue)) {
            $missingOptional += $cmd
        }
    }
    
    if ($missingRequired.Count -gt 0) {
        Write-Error "Missing required commands: $($missingRequired -join ', ')"
        Write-Error "Please install OpenSSH client"
        return $false
    }
    
    if ($missingOptional.Count -gt 0) {
        Write-Warning "Missing optional commands (may limit functionality): $($missingOptional -join ', ')"
    }
    
    return $true
}

# Test 7: Validate file paths and permissions
function Test-FilePathsAndPermissions {
    $scriptsToCheck = @(
        "$ProjectDir\deploy.sh",
        "$ScriptDir\smart-deploy-staging.ps1",
        "$ProjectDir\docker-exports\deploy-to-digitalocean.sh"
    )
    
    foreach ($script in $scriptsToCheck) {
        if (Test-Path $script) {
            try {
                # Try to read the file
                $content = Get-Content $script -TotalCount 1
                Write-Debug "Successfully read: $script"
            }
            catch {
                Write-Error "Cannot read script file: $script - $($_.Exception.Message)"
                return $false
            }
        }
    }
    
    return $true
}

# Main test runner
function Start-Tests {
    Write-Host "========================================"
    Write-Host "Smart Deployment Scripts Test Suite (PowerShell)"
    Write-Host "========================================"
    
    Invoke-Test "Required files exist" { Test-RequiredFiles }
    Invoke-Test "Manifest structure" { Test-ManifestStructure }
    Invoke-Test "Docker export files" { Test-DockerExportFiles }
    Invoke-Test "Script syntax" { Test-ScriptSyntax }
    Invoke-Test "Help functionality" { Test-HelpFunctionality }
    Invoke-Test "System dependencies" { Test-SystemDependencies }
    Invoke-Test "File paths and permissions" { Test-FilePathsAndPermissions }
    
    Write-Host ""
    Write-Host "========================================"
    Write-Host "Test Results Summary"
    Write-Host "========================================"
    Write-Success "Tests passed: $TestsPassed"
    
    if ($TestsFailed -gt 0) {
        Write-Error "Tests failed: $TestsFailed"
        Write-Host ""
        Write-Error "Some tests failed. Please fix the issues before deploying."
        return $false
    } else {
        Write-Host ""
        Write-Success "All tests passed! The deployment scripts are ready to use."
        Write-Host ""
        Write-Host "Next steps:"
        Write-Host "1. Ensure you have an SSH key set up for the target server"
        Write-Host "2. Run a dry-run deployment: .\scripts\smart-deploy-staging.ps1 -DryRun"
        Write-Host "3. If dry-run looks good, run actual deployment: .\scripts\smart-deploy-staging.ps1"
        Write-Host "4. Monitor the deployment logs and service health"
        return $true
    }
}

# Run tests
$success = Start-Tests

if (-not $success) {
    exit 1
}
