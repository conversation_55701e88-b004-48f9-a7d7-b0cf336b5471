# Smart deployment script for MVS-VR v2 staging environment (PowerShell)
# Supports incremental updates, missing file detection, and robust error handling

param(
    [string]$ServerIP = "**************",
    [string]$ServerUser = "root",
    [string]$SSHKey = "/root/.ssh/id_rsa",
    [string]$TargetDir = "/opt/mvs-vr-v2",
    [switch]$Force,
    [switch]$DryRun,
    [switch]$Verbose,
    [switch]$SkipVerification,
    [switch]$Help
)

# Script configuration
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectDir = Split-Path -Parent $ScriptDir
$DockerExportsDir = Join-Path $ProjectDir "docker-exports"
$ManifestFile = Join-Path $DockerExportsDir "export-manifest.json"

# Colors for output (Windows PowerShell compatible)
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Cyan = "Cyan"
}

# Logging functions
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Colors.Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Colors.Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Colors.Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Colors.Red
}

function Write-Debug {
    param([string]$Message)
    if ($Verbose) {
        Write-Host "[DEBUG] $Message" -ForegroundColor $Colors.Cyan
    }
}

# Show help
function Show-Help {
    @"
Smart Deployment Script for MVS-VR v2 Staging (PowerShell)

Usage: .\smart-deploy-staging.ps1 [OPTIONS]

OPTIONS:
    -ServerIP <IP>          Server IP address (default: $ServerIP)
    -ServerUser <USER>      SSH user (default: $ServerUser)
    -SSHKey <PATH>          SSH key path (default: $SSHKey)
    -TargetDir <DIR>        Target directory on server (default: $TargetDir)
    -Force                  Force transfer all files regardless of status
    -DryRun                 Show what would be done without executing
    -Verbose                Enable verbose output
    -SkipVerification       Skip file verification after transfer
    -Help                   Show this help message

EXAMPLES:
    # Basic deployment
    .\smart-deploy-staging.ps1

    # Dry run to see what would be transferred
    .\smart-deploy-staging.ps1 -DryRun

    # Force transfer all files
    .\smart-deploy-staging.ps1 -Force

    # Deploy to different server
    .\smart-deploy-staging.ps1 -ServerIP "*************" -ServerUser "ubuntu"

"@
}

# SSH command wrapper
function Invoke-SSH {
    param([string]$Command)
    
    Write-Debug "SSH: $Command"
    
    if ($DryRun) {
        Write-Host "[DRY RUN] Would execute SSH: $Command"
        return $true
    }
    
    try {
        # Use ssh command (requires OpenSSH or similar)
        $result = ssh -o ConnectTimeout=30 -o StrictHostKeyChecking=no -i $SSHKey "$ServerUser@$ServerIP" $Command
        return $LASTEXITCODE -eq 0
    }
    catch {
        Write-Error "SSH command failed: $Command"
        Write-Error $_.Exception.Message
        return $false
    }
}

# SCP command wrapper
function Copy-FileToServer {
    param([string]$SourcePath)
    
    $filename = Split-Path -Leaf $SourcePath
    Write-Debug "SCP: $SourcePath -> $ServerUser@$ServerIP`:$TargetDir/$filename"
    
    if ($DryRun) {
        Write-Host "[DRY RUN] Would copy: $SourcePath -> $ServerUser@$ServerIP`:$TargetDir/$filename"
        return $true
    }
    
    try {
        # Use scp command (requires OpenSSH or similar)
        scp -o ConnectTimeout=30 -i $SSHKey $SourcePath "$ServerUser@$ServerIP`:$TargetDir/$filename"
        return $LASTEXITCODE -eq 0
    }
    catch {
        Write-Error "SCP failed for: $filename"
        Write-Error $_.Exception.Message
        return $false
    }
}

# Test server connectivity
function Test-Connection {
    Write-Info "Testing connection to $ServerUser@$ServerIP..."
    
    if ($DryRun) {
        Write-Host "[DRY RUN] Would test connection"
        return $true
    }
    
    if (Invoke-SSH "echo 'Connection successful'") {
        Write-Success "Server connection established"
        return $true
    }
    else {
        Write-Error "Cannot connect to server"
        return $false
    }
}

# Check prerequisites
function Test-Prerequisites {
    Write-Info "Checking prerequisites..."
    
    # Check if docker-exports directory exists
    if (-not (Test-Path $DockerExportsDir)) {
        Write-Error "Docker exports directory not found: $DockerExportsDir"
        exit 1
    }
    
    # Check if manifest file exists
    if (-not (Test-Path $ManifestFile)) {
        Write-Error "Manifest file not found: $ManifestFile"
        exit 1
    }
    
    # Check SSH key
    if (-not (Test-Path $SSHKey)) {
        Write-Error "SSH key not found: $SSHKey"
        exit 1
    }
    
    # Check required commands
    $requiredCommands = @("ssh", "scp")
    foreach ($cmd in $requiredCommands) {
        if (-not (Get-Command $cmd -ErrorAction SilentlyContinue)) {
            Write-Error "Required command not found: $cmd"
            Write-Error "Please install OpenSSH or ensure it's in your PATH"
            exit 1
        }
    }
    
    Write-Success "Prerequisites check passed"
}

# Get expected files from manifest
function Get-ExpectedFiles {
    try {
        $manifest = Get-Content $ManifestFile | ConvertFrom-Json
        return $manifest.images.PSObject.Properties.Value.file
    }
    catch {
        Write-Error "Failed to parse manifest file: $ManifestFile"
        Write-Error $_.Exception.Message
        return @()
    }
}

# Get expected size from manifest
function Get-ExpectedSize {
    param([string]$Filename)
    
    try {
        $manifest = Get-Content $ManifestFile | ConvertFrom-Json
        $imageInfo = $manifest.images.PSObject.Properties.Value | Where-Object { $_.file -eq $Filename }
        if ($imageInfo) {
            return $imageInfo.size
        }
        return 0
    }
    catch {
        Write-Debug "Could not get expected size for: $Filename"
        return 0
    }
}

# Get version from manifest
function Get-Version {
    try {
        $manifest = Get-Content $ManifestFile | ConvertFrom-Json
        return $manifest.version
    }
    catch {
        return "unknown"
    }
}

# Check what files exist on remote server
function Get-RemoteFiles {
    Write-Debug "Checking existing files on remote server..."
    
    $command = "cd $TargetDir 2>/dev/null && find . -maxdepth 1 \( -name '*.tar.zip' -o -name '*.tar' -o -name 'export-manifest.json' \) -type f -exec sh -c 'echo \`"{} \$(stat -c%s \"{}\") \$(stat -c%Y \"{}\")\`\" 2>/dev/null || echo \`"{} 0 0\`\"' \; 2>/dev/null || echo ''"
    
    if ($DryRun) {
        Write-Host "[DRY RUN] Would check remote files"
        return @()
    }
    
    try {
        $result = ssh -o ConnectTimeout=30 -o StrictHostKeyChecking=no -i $SSHKey "$ServerUser@$ServerIP" $command
        if ($LASTEXITCODE -eq 0) {
            return $result
        }
        return @()
    }
    catch {
        Write-Debug "Could not get remote file list"
        return @()
    }
}

# Find files that need to be transferred
function Find-FilesToTransfer {
    Write-Info "Analyzing files to determine transfer requirements..."
    
    $remoteFilesInfo = Get-RemoteFiles
    $expectedFiles = Get-ExpectedFiles
    $filesToTransfer = @()
    
    $totalFiles = 0
    $upToDateFiles = 0
    $missingFiles = 0
    $sizeMismatchFiles = 0
    
    foreach ($filename in $expectedFiles) {
        if ([string]::IsNullOrEmpty($filename)) {
            continue
        }
        
        $totalFiles++
        
        $localFile = Join-Path $DockerExportsDir $filename
        $expectedSize = Get-ExpectedSize $filename
        
        # Check if local file exists
        if (-not (Test-Path $localFile)) {
            Write-Warning "Local file missing: $filename"
            continue
        }
        
        # Get actual local file size
        $actualLocalSize = (Get-Item $localFile).Length
        
        # Parse remote file info
        $remoteInfo = $remoteFilesInfo | Where-Object { $_ -match "\.\/$filename " }
        $remoteSize = 0
        
        if ($remoteInfo) {
            $parts = $remoteInfo -split '\s+'
            if ($parts.Length -ge 2) {
                $remoteSize = [int64]$parts[1]
            }
        }
        
        $shouldTransfer = $false
        $reason = ""
        
        # Force transfer if requested
        if ($Force) {
            $shouldTransfer = $true
            $reason = "forced"
        }
        # Check if file is missing on remote
        elseif ($remoteSize -eq 0) {
            $shouldTransfer = $true
            $reason = "missing on remote"
            $missingFiles++
        }
        # Check size mismatch
        elseif ($actualLocalSize -ne $remoteSize) {
            $shouldTransfer = $true
            $reason = "size mismatch (local: $actualLocalSize, remote: $remoteSize)"
            $sizeMismatchFiles++
        }
        # Check if local file doesn't match expected size from manifest
        elseif ($expectedSize -gt 0 -and $actualLocalSize -ne $expectedSize) {
            Write-Warning "Local file size doesn't match manifest for $filename (actual: $actualLocalSize, expected: $expectedSize)"
            $shouldTransfer = $true
            $reason = "local file corrupted"
        }
        else {
            $upToDateFiles++
        }
        
        if ($shouldTransfer) {
            Write-Info "Will transfer $filename`: $reason"
            $filesToTransfer += $filename
        }
        else {
            Write-Debug "File up to date: $filename"
        }
    }
    
    # Always include manifest file
    if ($Force -or -not (Test-Path (Join-Path $DockerExportsDir "export-manifest.json"))) {
        $filesToTransfer += "export-manifest.json"
    }
    
    # Print summary
    Write-Info "File analysis summary:"
    Write-Info "  Total files: $totalFiles"
    Write-Info "  Up to date: $upToDateFiles"
    Write-Info "  Missing on remote: $missingFiles"
    Write-Info "  Size mismatches: $sizeMismatchFiles"
    Write-Info "  Files to transfer: $($filesToTransfer.Count)"
    
    return $filesToTransfer
}

# Transfer files with progress tracking
function Copy-FilesToServer {
    param([array]$FilesToTransfer)

    if ($FilesToTransfer.Count -eq 0) {
        Write-Info "No files need to be transferred"
        return $true
    }

    Write-Info "Transferring $($FilesToTransfer.Count) files..."

    $transferred = 0
    $failed = 0
    $totalSize = 0

    # Calculate total size
    foreach ($filename in $FilesToTransfer) {
        $localFile = Join-Path $DockerExportsDir $filename
        if (Test-Path $localFile) {
            $size = (Get-Item $localFile).Length
            $totalSize += $size
        }
    }

    $totalSizeHuman = if ($totalSize -gt 1GB) { "{0:N2} GB" -f ($totalSize / 1GB) }
                     elseif ($totalSize -gt 1MB) { "{0:N2} MB" -f ($totalSize / 1MB) }
                     elseif ($totalSize -gt 1KB) { "{0:N2} KB" -f ($totalSize / 1KB) }
                     else { "$totalSize bytes" }

    Write-Info "Total transfer size: $totalSizeHuman"

    # Transfer each file
    foreach ($filename in $FilesToTransfer) {
        $localFile = Join-Path $DockerExportsDir $filename

        if (Test-Path $localFile) {
            $fileSize = (Get-Item $localFile).Length
            $fileSizeHuman = if ($fileSize -gt 1GB) { "{0:N2} GB" -f ($fileSize / 1GB) }
                            elseif ($fileSize -gt 1MB) { "{0:N2} MB" -f ($fileSize / 1MB) }
                            elseif ($fileSize -gt 1KB) { "{0:N2} KB" -f ($fileSize / 1KB) }
                            else { "$fileSize bytes" }

            Write-Info "Transferring: $filename ($fileSizeHuman)"

            if (Copy-FileToServer $localFile) {
                Write-Success "Transferred: $filename"
                $transferred++
            }
            else {
                Write-Error "Failed to transfer: $filename"
                $failed++
            }
        }
        else {
            Write-Warning "Local file not found, skipping: $filename"
            $failed++
        }
    }

    Write-Info "Transfer summary: $transferred successful, $failed failed"

    return $failed -eq 0
}

# Verify transferred files
function Test-TransferredFiles {
    param([array]$FilesToVerify)

    if ($SkipVerification) {
        Write-Info "Skipping file verification (-SkipVerification specified)"
        return $true
    }

    Write-Info "Verifying transferred files..."

    $verified = 0
    $failed = 0

    foreach ($filename in $FilesToVerify) {
        if ($filename -eq "export-manifest.json") {
            # Just check if manifest exists
            if (Invoke-SSH "test -f $TargetDir/$filename") {
                Write-Success "Verified: $filename (exists)"
                $verified++
            }
            else {
                Write-Error "Verification failed: $filename (missing)"
                $failed++
            }
            continue
        }

        $expectedSize = Get-ExpectedSize $filename

        if ($expectedSize -gt 0) {
            $command = "cd $TargetDir && stat -c%s '$filename' 2>/dev/null || echo '0'"
            $remoteSize = 0

            try {
                $result = ssh -o ConnectTimeout=30 -o StrictHostKeyChecking=no -i $SSHKey "$ServerUser@$ServerIP" $command
                if ($LASTEXITCODE -eq 0) {
                    $remoteSize = [int64]$result
                }
            }
            catch {
                Write-Debug "Could not get remote file size for: $filename"
            }

            if ($remoteSize -eq $expectedSize) {
                $remoteSizeHuman = if ($remoteSize -gt 1GB) { "{0:N2} GB" -f ($remoteSize / 1GB) }
                                  elseif ($remoteSize -gt 1MB) { "{0:N2} MB" -f ($remoteSize / 1MB) }
                                  elseif ($remoteSize -gt 1KB) { "{0:N2} KB" -f ($remoteSize / 1KB) }
                                  else { "$remoteSize bytes" }

                Write-Success "Verified: $filename (size: $remoteSizeHuman)"
                $verified++
            }
            else {
                Write-Error "Verification failed for $filename (expected: $expectedSize, actual: $remoteSize)"
                $failed++
            }
        }
        else {
            Write-Warning "Cannot verify $filename (no expected size in manifest)"
        }
    }

    Write-Info "Verification summary: $verified verified, $failed failed"

    return $failed -eq 0
}

# Setup remote environment
function Initialize-RemoteEnvironment {
    Write-Info "Setting up remote environment..."

    # Create target directory
    Invoke-SSH "mkdir -p $TargetDir" | Out-Null

    # Create backup directory
    Invoke-SSH "mkdir -p $TargetDir/backups" | Out-Null

    # Check Docker and Docker Compose
    if (-not (Invoke-SSH "command -v docker >/dev/null 2>&1")) {
        Write-Error "Docker not found on remote server"
        exit 1
    }

    if (-not (Invoke-SSH "command -v docker-compose >/dev/null 2>&1")) {
        Write-Error "Docker Compose not found on remote server"
        exit 1
    }

    Write-Success "Remote environment ready"
}

# Deploy services on remote server
function Start-Services {
    Write-Info "Deploying services on remote server..."

    $version = Get-Version

    # Load Docker images
    Write-Info "Loading Docker images (version: $version)..."
    $loadCommand = "cd $TargetDir && if [ -f load-images.sh ]; then chmod +x load-images.sh && ./load-images.sh; elif [ -f load-images.ps1 ]; then powershell -File load-images.ps1; else echo 'No image loader script found'; fi"
    Invoke-SSH $loadCommand | Out-Null

    # Copy environment file if it exists
    $envFile = Join-Path $ProjectDir ".env.staging"
    if (Test-Path $envFile) {
        Write-Info "Copying staging environment file..."
        Copy-FileToServer $envFile | Out-Null
        Invoke-SSH "cd $TargetDir && mv .env.staging .env" | Out-Null
    }
    elseif (Test-Path (Join-Path $ProjectDir ".env")) {
        Write-Info "Copying environment file..."
        Copy-FileToServer (Join-Path $ProjectDir ".env") | Out-Null
    }

    # Stop existing services
    Write-Info "Stopping existing services..."
    Invoke-SSH "cd $TargetDir && docker-compose down || true" | Out-Null

    # Start services
    Write-Info "Starting services..."
    Invoke-SSH "cd $TargetDir && docker-compose pull && docker-compose up -d" | Out-Null

    # Wait for services to start
    Write-Info "Waiting for services to start..."
    Start-Sleep -Seconds 10

    Write-Success "Services deployed successfully"
}

# Check service health
function Test-ServiceHealth {
    Write-Info "Checking service health..."

    # Show container status
    $psResult = ssh -o ConnectTimeout=30 -o StrictHostKeyChecking=no -i $SSHKey "$ServerUser@$ServerIP" "cd $TargetDir && docker-compose ps"
    Write-Host $psResult

    # Check for any failed containers
    $failedContainers = ssh -o ConnectTimeout=30 -o StrictHostKeyChecking=no -i $SSHKey "$ServerUser@$ServerIP" "cd $TargetDir && docker-compose ps --services --filter status=exited 2>/dev/null || echo ''"

    if ($failedContainers) {
        Write-Warning "Some containers have exited:"
        Write-Host $failedContainers
    }
    else {
        Write-Success "All services appear to be running"
    }
}

# Create backup before deployment
function New-Backup {
    Write-Info "Creating backup before deployment..."

    $backupDir = "$TargetDir/backups/$(Get-Date -Format 'yyyyMMdd_HHmmss')"

    # Create backup directory
    Invoke-SSH "mkdir -p $backupDir" | Out-Null

    # Backup existing files
    $backupCommand = "cd $TargetDir && find . -maxdepth 1 -name '*.tar*' -o -name 'export-manifest.json' -o -name '.env' | xargs -I {} cp {} $backupDir/ 2>/dev/null || true"
    Invoke-SSH $backupCommand | Out-Null

    Write-Success "Backup created at: $backupDir"
}

# Main deployment function
function Start-Deployment {
    $startTime = Get-Date

    Write-Info "Starting smart deployment for MVS-VR v2 staging environment"
    Write-Info "Target: $ServerUser@$ServerIP`:$TargetDir"
    Write-Info "Version: $(Get-Version)"

    # Check prerequisites
    Test-Prerequisites

    # Test connection
    if (-not (Test-Connection)) {
        exit 1
    }

    # Setup remote environment
    Initialize-RemoteEnvironment

    # Create backup
    if (-not $DryRun) {
        New-Backup
    }

    # Find files that need to be transferred
    $filesToTransfer = Find-FilesToTransfer

    if ($filesToTransfer.Count -eq 0) {
        Write-Info "All files are up to date on the server"
    }
    else {
        # Transfer files
        if (-not (Copy-FilesToServer $filesToTransfer)) {
            Write-Error "File transfer failed"
            exit 1
        }

        # Verify transferred files
        if (-not (Test-TransferredFiles $filesToTransfer)) {
            Write-Error "File verification failed"
            exit 1
        }
    }

    # Deploy services
    if (-not $DryRun) {
        Start-Services
        Test-ServiceHealth
    }
    else {
        Write-Host "[DRY RUN] Would deploy services and check health"
    }

    $endTime = Get-Date
    $duration = ($endTime - $startTime).TotalSeconds

    Write-Success "Smart deployment completed successfully!"
    Write-Info "Total deployment time: $([math]::Round($duration, 2))s"

    # Show final status
    if (-not $DryRun) {
        Write-Info "=== Deployment Summary ==="
        Write-Info "Server: $ServerIP"
        Write-Info "Target Directory: $TargetDir"
        Write-Info "Version: $(Get-Version)"
        Write-Info "Files Transferred: $($filesToTransfer.Count)"
        Write-Info "Deployment Time: $([math]::Round($duration, 2))s"
        Write-Host ""
        Write-Info "=== Service URLs ==="
        Write-Info "API: https://api.mvs.kanousai.com"
        Write-Info "Admin: https://admin.mvs.kanousai.com"
        Write-Info "Staging: https://staging.mvs.kanousai.com"
    }
}

# Main execution
if ($Help) {
    Show-Help
    exit 0
}

# Run main function
Start-Deployment
